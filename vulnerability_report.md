# NoDepeg Policy Bypass in redeemSharesInKind Function Leads to MEV Extraction During Stablecoin Depeg Recovery

## Brief/Intro

The `redeemSharesInKind` function with `assetsToSkip` parameter allows users to bypass NoDepeg policy protections and selectively redeem depegged stablecoins during price recovery periods. This enables MEV extraction where attackers can purchase undervalued assets from the fund using stale oracle prices while selling them at market prices, causing direct financial losses to remaining fund participants.

## Vulnerability Details

### Root Cause
The NoDepeg policy in Enzyme Finance is designed to prevent selective redemption of depegged stablecoins to protect fund participants from unfair value extraction. However, this protection only applies to the `redeemSharesForSpecificAssets` function, while the `redeemSharesInKind` function with the `assetsToSkip` parameter provides an alternative pathway to achieve the same selective redemption without policy enforcement.

### Attack Mechanism
1. **Setup Phase**: A stablecoin (e.g., USDC) experiences depeg to $0.95, affecting both market prices and the fund's oracle
2. **Recovery Phase**: Market prices recover to $1.00, but the fund's oracle lags behind, still showing $0.95
3. **Exploitation**: Attacker calls `redeemSharesInKind` with `assetsToSkip` parameter, excluding all assets except the undervalued USDC
4. **Arbitrage**: Attacker receives USDC at the stale oracle price ($0.95) and immediately sells on the market at current price ($1.00)

### Technical Implementation Gap
The vulnerability exists because:
- `redeemSharesForSpecificAssets` → Protected by NoDepeg policy
- `redeemSharesInKind` with selective `assetsToSkip` → No policy protection, same economic effect

This creates an asymmetric protection model where the intended security measure can be completely bypassed through an alternative function with equivalent capabilities.

### Attack Constraints
- Attacker can only redeem assets proportional to their share ownership in the fund
- Requires oracle lag during stablecoin price recovery
- Requires sufficient liquidity of the target asset in the fund portfolio

## Impact Details

### Classification: CRITICAL - MEV (Miner-Extractable Value)

### Financial Impact Calculation
**Per-attack profit**: `(Market_Price - Oracle_Price) × USDC_Amount_Redeemed`
**Example**: `($1.00 - $0.95) × 100,000 USDC = $5,000 profit`

### Damage to Fund Participants
1. **Direct Losses**: Fund sells assets below market value, reducing NAV for remaining investors
2. **Wealth Transfer**: Value transfers from fund participants to the attacker
3. **Systemic Risk**: Repeated exploitation during volatile periods can significantly drain fund value

### Economic Feasibility
- **High Probability**: Stablecoin depegs occur regularly (USDC March 2023, DAI November 2022)
- **Oracle Lag**: Common during high volatility periods when price feeds update with delays
- **Scalability**: Attack scales with attacker's fund ownership and available depegged assets

### Real-World Scenario Impact
For a fund with $10M AUM and 10% USDC allocation during a 5% depeg recovery:
- Available USDC: $1,000,000
- Attacker with 10% fund ownership can extract: $5,000 profit
- Fund participants lose: $5,000 in asset value

## Impact Justification

This vulnerability meets the CRITICAL MEV classification because:
1. **MEV Definition Met**: "somebody not participating in the protocol is making a profit" through price arbitrage
2. **Bypass of Security Controls**: Circumvents intended NoDepeg policy protections
3. **Direct Financial Harm**: Causes measurable losses to fund participants
4. **In-Scope Vulnerability**: Based on code logic flaw, not external depeg event itself

The attack exploits a gap in the protocol's security model rather than relying solely on external market conditions, making it a valid in-scope vulnerability under the bug bounty program.

### Scope Analysis
This vulnerability is **IN-SCOPE** because:
- The attack is based on a **code logic flaw** (missing NoDepeg policy enforcement)
- It's not relying solely on external stablecoin depegging
- The bug bounty exclusion states: "attacks involving the depegging of an external stablecoin where the attacker does not directly cause the depegging **due to a bug in code**"
- Here, the depegging is NOT caused by the bug - the bug is the missing protection mechanism

### Recommended Fix
Extend NoDepeg policy enforcement to the `redeemSharesInKind` function, specifically when `assetsToSkip` parameter is used to selectively exclude assets, creating the same economic effect as `redeemSharesForSpecificAssets`.

## References

- Enzyme Finance NoDepeg Policy Documentation
- `redeemSharesInKind` function implementation
- `redeemSharesForSpecificAssets` function implementation  
- Historical stablecoin depeg events (USDC March 2023, DAI November 2022)
- Enzyme Finance Bug Bounty Program Scope and Impact Classifications
